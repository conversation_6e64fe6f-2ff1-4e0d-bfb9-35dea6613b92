# CryptoDo - Trello-like Application

A modern Trello-like application built with Next.js, NestJS, MongoDB, and GraphQL.

## Features

- Drag and drop cards between swimlanes
- Card priorities (Low, Medium, High, Top)
- Comments with WYSIWYG editor
- Checklists with completion tracking
- EVM addresses and private keys
- HTTP links
- Export/Import functionality
- Card sorting by name or priority
- Professional UI design
- Card timestamps (created/updated)

## Technology Stack

- **Frontend**: Next.js with TypeScript, Apollo Client, React Beautiful DnD
- **Backend**: NestJS with TypeScript, GraphQL, Apollo Server
- **Database**: MongoDB
- **Package Manager**: pnpm
- **Containerization**: Docker and Docker Compose

## Getting Started

### Prerequisites

- Docker and Docker Compose installed on your machine

### Running the Application

1. Clone the repository
2. Navigate to the project directory
3. Run the following command to start all services:

```bash
docker-compose up -d
```

4. Access the application:
   - Frontend: http://localhost:4545
   - Backend GraphQL Playground: http://localhost:3001/graphql

### Development

#### Frontend

```bash
cd frontend
npm install
npm run dev
```

#### Backend

```bash
cd backend
npm install
npm run start:dev
```

## Project Structure

```
.
├── frontend/                # Next.js frontend application
│   ├── src/
│   │   ├── components/      # React components
│   │   ├── lib/             # Utility functions and libraries
│   │   ├── pages/           # Next.js pages
│   │   ├── styles/          # Global styles
│   │   └── types/           # TypeScript type definitions
│   ├── public/              # Static assets
│   └── Dockerfile           # Frontend Docker configuration
│
├── backend/                 # Nest.js backend application
│   ├── src/
│   │   ├── board/           # Board module
│   │   ├── swimlane/        # Swimlane module
│   │   ├── card/            # Card module
│   │   ├── comment/         # Comment module
│   │   └── main.ts          # Application entry point
│   └── Dockerfile           # Backend Docker configuration
│
└── docker-compose.yml       # Docker Compose configuration
```

## License

This project is licensed under the MIT License.
