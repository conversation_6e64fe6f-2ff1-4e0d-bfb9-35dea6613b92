import React, { useState, useRef, useEffect } from 'react';
import { gql, useQuery, useMutation, useApolloClient } from '@apollo/client';
import SearchBox from './SearchBox';

// GraphQL query to get all board data for export
const GET_BOARD_FOR_EXPORT = gql`
  query GetBoardForExport {
    board {
      _id
      name
      swimlanes {
        _id
        title
        position
        cards {
          _id
          title
          content
          color
          link
          evmAddress
          privateKey
          position
          priority
          comments {
            _id
            content
            createdAt
            updatedAt
          }
          checklistItems {
            _id
            text
            completed
            position
          }
        }
      }
    }
  }
`;

// GraphQL mutations for import functionality
const CREATE_SWIMLANE = gql`
  mutation CreateSwimlane($createSwimlaneInput: CreateSwimlaneInput!) {
    createSwimlane(createSwimlaneInput: $createSwimlaneInput) {
      _id
      title
      position
    }
  }
`;

const DELETE_SWIMLANE = gql`
  mutation DeleteSwimlane($id: ID!) {
    deleteSwimlane(id: $id) {
      _id
    }
  }
`;

const ADD_CARD = gql`
  mutation AddCard($title: String!, $swimlaneId: ID!) {
    addCard(title: $title, swimlaneId: $swimlaneId) {
      _id
      title
    }
  }
`;

const UPDATE_CARD = gql`
  mutation UpdateCard($id: ID!, $title: String, $content: String, $color: String, $link: String, $evmAddress: String, $privateKey: String, $priority: Priority) {
    updateCard(id: $id, title: $title, content: $content, color: $color, link: $link, evmAddress: $evmAddress, privateKey: $privateKey, priority: $priority) {
      _id
      priority
    }
  }
`;

const ADD_COMMENT = gql`
  mutation AddComment($cardId: ID!, $content: String!) {
    addComment(cardId: $cardId, content: $content) {
      _id
    }
  }
`;

const ADD_CHECKLIST_ITEM = gql`
  mutation AddChecklistItem($createChecklistItemInput: CreateChecklistItemInput!) {
    addChecklistItem(createChecklistItemInput: $createChecklistItemInput) {
      _id
    }
  }
`;

interface HeaderProps {
    searchQuery: string;
    setSearchQuery: (query: string) => void;
}

const Header: React.FC<HeaderProps> = ({ searchQuery, setSearchQuery }) => {
    const [showMenu, setShowMenu] = useState(false);
    const [isImporting, setIsImporting] = useState(false);
    const menuRef = useRef<HTMLDivElement>(null);
    const fileInputRef = useRef<HTMLInputElement>(null);

    // Apollo client for cache operations
    const client = useApolloClient();

    // Query to get all board data
    const { loading, error, data, refetch } = useQuery(GET_BOARD_FOR_EXPORT);

    // Mutations for import functionality
    const [createSwimlane] = useMutation(CREATE_SWIMLANE);
    const [deleteSwimlane] = useMutation(DELETE_SWIMLANE);
    const [addCard] = useMutation(ADD_CARD);
    const [updateCard] = useMutation(UPDATE_CARD);
    const [addComment] = useMutation(ADD_COMMENT);
    const [addChecklistItem] = useMutation(ADD_CHECKLIST_ITEM);

    // Handle export functionality
    const handleExport = () => {
        if (loading || error || !data) {
            alert('Unable to export data at this time. Please try again.');
            return;
        }

        // Create a JSON blob with the board data
        const jsonData = JSON.stringify(data.board, null, 2);
        const blob = new Blob([jsonData], { type: 'application/json' });

        // Create a download link and trigger it
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `cryptodo-export-${new Date().toISOString().split('T')[0]}.json`;
        document.body.appendChild(a);
        a.click();

        // Clean up
        setTimeout(() => {
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
            setShowMenu(false);
        }, 100);
    };

    // Handle import functionality
    const handleImportClick = () => {
        if (fileInputRef.current) {
            fileInputRef.current.click();
        }
    };

    // Clear the board by deleting all swimlanes
    const clearBoard = async () => {
        if (!data || !data.board || !data.board.swimlanes) return;

        for (const swimlane of data.board.swimlanes) {
            try {
                await deleteSwimlane({
                    variables: {
                        id: swimlane._id
                    }
                });
            } catch (error) {
                console.error('Error deleting swimlane:', error);
            }
        }

        // Refresh the data
        await refetch();
    };

    const handleFileChange = async (event: React.ChangeEvent<HTMLInputElement>) => {
        const file = event.target.files?.[0];
        if (!file) return;

        // Confirm before proceeding
        if (!window.confirm('Importing will replace all current data. Are you sure you want to continue?')) {
            // Reset the file input
            if (fileInputRef.current) {
                fileInputRef.current.value = '';
            }
            return;
        }

        const reader = new FileReader();
        reader.onload = async (e) => {
            try {
                setIsImporting(true);
                setShowMenu(false);

                const importedData = JSON.parse(e.target?.result as string);

                // Process the imported data
                if (!importedData || !importedData.swimlanes) {
                    throw new Error('Invalid import data format');
                }

                // Get the current board ID
                const boardId = data?.board?._id;
                if (!boardId) {
                    throw new Error('Could not determine board ID');
                }

                // Clear the board first
                await clearBoard();

                // Create a mapping of old IDs to new IDs
                const idMap: {
                    swimlanes: { [key: string]: string };
                    cards: { [key: string]: string };
                } = {
                    swimlanes: {},
                    cards: {}
                };

                // Import swimlanes
                for (const swimlane of importedData.swimlanes) {
                    try {
                        // Create swimlane
                        const result = await createSwimlane({
                            variables: {
                                createSwimlaneInput: {
                                    title: swimlane.title,
                                    boardId: boardId
                                }
                            }
                        });

                        const newSwimlaneId = result.data.createSwimlane._id;
                        idMap.swimlanes[swimlane._id] = newSwimlaneId;

                        // Import cards for this swimlane
                        if (swimlane.cards && swimlane.cards.length > 0) {
                            for (const card of swimlane.cards) {
                                try {
                                    // Create card
                                    const cardResult = await addCard({
                                        variables: {
                                            title: card.title,
                                            swimlaneId: newSwimlaneId
                                        }
                                    });

                                    const newCardId = cardResult.data.addCard._id;
                                    idMap.cards[card._id] = newCardId;

                                    // Update card with additional fields
                                    await updateCard({
                                        variables: {
                                            id: newCardId,
                                            content: card.content || '',
                                            color: card.color || '',
                                            link: card.link || '',
                                            evmAddress: card.evmAddress || null,
                                            privateKey: card.privateKey || null,
                                            priority: card.priority || 'MEDIUM'
                                        }
                                    });

                                    // Import comments
                                    if (card.comments && card.comments.length > 0) {
                                        for (const comment of card.comments) {
                                            await addComment({
                                                variables: {
                                                    cardId: newCardId,
                                                    content: comment.content
                                                }
                                            });
                                        }
                                    }

                                    // Import checklist items
                                    if (card.checklistItems && card.checklistItems.length > 0) {
                                        for (const item of card.checklistItems) {
                                            await addChecklistItem({
                                                variables: {
                                                    createChecklistItemInput: {
                                                        text: item.text,
                                                        completed: item.completed,
                                                        cardId: newCardId
                                                    }
                                                }
                                            });
                                        }
                                    }
                                } catch (cardError) {
                                    console.error('Error importing card:', cardError);
                                }
                            }
                        }
                    } catch (swimlaneError) {
                        console.error('Error importing swimlane:', swimlaneError);
                    }
                }

                // Refresh the data
                await refetch();

                // Reset the file input
                if (fileInputRef.current) {
                    fileInputRef.current.value = '';
                }

                alert('Import completed successfully!');
            } catch (error) {
                alert(`Error importing data: ${error instanceof Error ? error.message : 'Unknown error'}`);
                console.error('Import error:', error);
            } finally {
                setIsImporting(false);
            }
        };

        reader.readAsText(file);
    };

    // Toggle menu
    const toggleMenu = () => {
        setShowMenu(!showMenu);
    };

    // Close menu when clicking outside
    useEffect(() => {
        const handleClickOutside = (event: MouseEvent) => {
            if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
                setShowMenu(false);
            }
        };

        document.addEventListener('mousedown', handleClickOutside);
        return () => {
            document.removeEventListener('mousedown', handleClickOutside);
        };
    }, []);

    return (
        <header className="app-header">
            <div className="app-header__logo">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="white">
                    <path
                        d="M11.5 12.5v-1.9c1.4 0 2.6-0.1 2.6-1.6 0-1.4-1.1-1.6-2.6-1.6v-1.9h-1v1.9c-0.3 0-0.6 0-1 0v-1.9h-1v1.9c-0.8 0-1.5 0-1.5 0v1h1c0.6 0 0.7 0.2 0.7 0.6v3.8c0 0.5-0.3 0.6-0.8 0.6h-1v1h1.5c0.5 0 1 0 1.5 0v1.9h1v-1.9c0.4 0 0.7 0 1 0v1.9h1v-1.9c2.2-0.1 3.3-1 3.3-2.5 0-1.6-1-2.1-3.7-2.4zm-1-3.4c0.7 0 2.1 0 2.1 1.2 0 1.1-1.4 1.1-2.1 1.1v-2.3zm0 5.8v-2.5c0.9 0 2.5-0.1 2.5 1.3 0 1.3-1.6 1.2-2.5 1.2z"/>
                    <circle cx="12" cy="12" r="10" stroke="white" stroke-width="1.5" fill="none"/>
                </svg>
                Cryptodo
            </div>

            <div className="app-header__middle">
                {/* Middle section can be used for other content */}
            </div>

            <div className="app-header__actions">
                <SearchBox searchQuery={searchQuery} setSearchQuery={setSearchQuery} />
                <button className="app-header__menu-btn" onClick={toggleMenu}>
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="white">
                        <circle cx="12" cy="12" r="1" />
                        <circle cx="12" cy="5" r="1" />
                        <circle cx="12" cy="19" r="1" />
                    </svg>
                </button>

                {isImporting && (
                    <div className="app-header__importing">
                        Importing data...
                    </div>
                )}

                {showMenu && (
                    <div className="app-header__menu" ref={menuRef}>
                        <button className="app-header__menu-item" onClick={handleExport}>
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                <path d="M21 15v4a2 2 0 01-2 2H5a2 2 0 01-2-2v-4" />
                                <polyline points="7 10 12 15 17 10" />
                                <line x1="12" y1="15" x2="12" y2="3" />
                            </svg>
                            Export Board
                        </button>
                        <button className="app-header__menu-item" onClick={handleImportClick}>
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                <path d="M21 15v4a2 2 0 01-2 2H5a2 2 0 01-2-2v-4" />
                                <polyline points="17 8 12 3 7 8" />
                                <line x1="12" y1="3" x2="12" y2="15" />
                            </svg>
                            Import Board
                        </button>
                        <input
                            type="file"
                            ref={fileInputRef}
                            style={{ display: 'none' }}
                            accept=".json"
                            onChange={handleFileChange}
                        />
                    </div>
                )}
            </div>
        </header>
    );
};

export default Header;
