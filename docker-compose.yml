services:
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    ports:
      - "4545:4545"
    environment:
      - NEXT_PUBLIC_GRAPHQL_URL=http://localhost:4544/graphql
    depends_on:
      - backend
    networks:
      - cryptodo-network

  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    ports:
      - "4544:4544"
    environment:
      - MONGODB_URI=mongodb://mongodb:27017/cryptodo
      - FRONTEND_URL=http://localhost:4545
    depends_on:
      - mongodb
    networks:
      - cryptodo-network

  mongodb:
    image: mongo:7
    ports:
      - "27018:27017"
    volumes:
      - mongodb_data:/data/db
    networks:
      - cryptodo-network

volumes:
  mongodb_data:

networks:
  cryptodo-network:
    driver: bridge